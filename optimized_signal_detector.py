#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的股票信号检测器
集成颜色识别和OCR的混合方案，提供最佳性能和准确率
"""

import cv2
import numpy as np
import json
import time
import os
import logging
from typing import Tuple, Optional, Dict, Any, Union
from PIL import Image

class OptimizedSignalDetector:
    """优化的信号检测器 - 支持颜色识别和OCR混合模式"""
    
    def __init__(self, ocr_manager=None, config: Dict[str, Any] = None):
        """
        初始化检测器
        
        Args:
            ocr_manager: OCR管理器实例
            config: 配置字典
        """
        self.ocr_manager = ocr_manager
        self.config = config or self._load_default_config()
        self.logger = logging.getLogger(__name__)
        
        # 信号映射
        self.signal_names = {
            'open': '开仓',
            'close': '清仓', 
            'hold': '持仓',
            'empty': '空仓'
        }
        
        # 反向映射
        self.signal_reverse = {v: k for k, v in self.signal_names.items()}
        
        # 从配置或优化结果加载颜色范围
        self.color_ranges = self._load_optimized_ranges()
        
        # 设置参数
        signal_config = self.config.get('signal_recognition', {})
        color_config = signal_config.get('color_recognition', {})
        
        self.recognition_mode = signal_config.get('recognition_mode', 'hybrid')
        self.color_enabled = color_config.get('enabled', True)
        self.bg_threshold = 50
        self.min_pixels = 20
        self.confidence_threshold = 0.6
        
        # 统计信息
        self.stats = {
            'color_success': 0,
            'color_fallback': 0,
            'ocr_success': 0,
            'total_attempts': 0,
            'avg_color_time': 0,
            'avg_ocr_time': 0
        }
        
        self.logger.info(f"信号检测器初始化完成，模式: {self.recognition_mode}")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            'signal_recognition': {
                'recognition_mode': 'hybrid',
                'color_recognition': {
                    'enabled': True
                }
            }
        }
    
    def _load_optimized_ranges(self) -> Dict[str, list]:
        """加载优化的颜色范围"""
        # 优先从优化文件加载
        optimized_file = 'optimized_color_ranges.json'
        if os.path.exists(optimized_file):
            try:
                with open(optimized_file, 'r', encoding='utf-8') as f:
                    ranges_data = json.load(f)
                    
                # 转换为numpy数组格式
                color_ranges = {}
                for signal_type, ranges in ranges_data.items():
                    color_ranges[signal_type] = []
                    for range_def in ranges:
                        color_ranges[signal_type].append({
                            'lower': np.array(range_def['lower']),
                            'upper': np.array(range_def['upper'])
                        })
                
                self.logger.info("已加载优化的颜色范围配置")
                return color_ranges
                
            except Exception as e:
                self.logger.warning(f"加载优化颜色范围失败: {e}")
        
        # 使用软件更新后的优化范围 (2025.08.19)
        return {
            'open': [  # 红色系文字 - 开仓 (HSV: 177, 147, 149)
                {'lower': np.array([170, 100, 100]), 'upper': np.array([180, 200, 200])},
                {'lower': np.array([0, 100, 100]), 'upper': np.array([10, 200, 200])}
            ],
            'close': [  # 橙色系文字 - 清仓 (HSV: 18, 252, 172)
                {'lower': np.array([8, 200, 120]), 'upper': np.array([28, 255, 220])}
            ],
            'hold': [  # 绿色系文字 - 持仓 (HSV: 67, 134, 126)
                {'lower': np.array([52, 80, 80]), 'upper': np.array([82, 180, 180])}
            ],
            'empty': [  # 灰色 - 空仓 (HSV: 0, 0, 160)
                {'lower': np.array([0, 0, 110]), 'upper': np.array([180, 30, 210])}
            ]
        }
    
    def detect_signal_from_image(self, image: Union[str, np.ndarray, Image.Image]) -> Tuple[Optional[str], float, Dict[str, Any]]:
        """
        从图像检测信号
        
        Args:
            image: 图像路径、numpy数组或PIL图像
            
        Returns:
            (signal, confidence, metadata)
        """
        self.stats['total_attempts'] += 1
        start_time = time.time()
        
        # 转换图像格式
        img_array = self._convert_to_array(image)
        if img_array is None:
            return None, 0.0, {'error': 'Invalid image'}
        
        metadata = {
            'recognition_mode': self.recognition_mode,
            'color_enabled': self.color_enabled,
            'methods_tried': []
        }
        
        # 根据模式选择识别策略
        if self.recognition_mode == 'color' or (self.recognition_mode == 'hybrid' and self.color_enabled):
            # 尝试颜色识别
            color_start = time.time()
            signal, confidence, pixel_counts = self._detect_by_color(img_array)
            color_time = time.time() - color_start
            
            metadata['methods_tried'].append({
                'method': 'color',
                'signal': signal,
                'confidence': confidence,
                'time': color_time,
                'pixel_counts': pixel_counts
            })
            
            # 更新统计
            self.stats['avg_color_time'] = (self.stats['avg_color_time'] * self.stats['total_attempts'] + color_time) / (self.stats['total_attempts'] + 1)
            
            if signal and signal != 'unknown' and confidence >= self.confidence_threshold:
                self.stats['color_success'] += 1
                total_time = time.time() - start_time
                metadata['total_time'] = total_time
                metadata['final_method'] = 'color'
                
                return self.signal_names.get(signal, signal), confidence, metadata
        
        # 颜色识别失败或不启用，尝试OCR
        if self.recognition_mode in ['ocr', 'hybrid'] and self.ocr_manager:
            ocr_start = time.time()
            
            # 转换为PIL图像供OCR使用
            pil_image = self._convert_to_pil(img_array)
            if pil_image:
                try:
                    # 使用OCR识别
                    raw_text = self.ocr_manager.recognize_text(pil_image)
                    if raw_text:
                        # 清洗和解析OCR结果
                        cleaned_text = self._clean_ocr_text(raw_text)
                        signal = self._parse_signal_content(cleaned_text)
                        
                        ocr_time = time.time() - ocr_start
                        self.stats['avg_ocr_time'] = (self.stats['avg_ocr_time'] * self.stats['total_attempts'] + ocr_time) / (self.stats['total_attempts'] + 1)
                        
                        metadata['methods_tried'].append({
                            'method': 'ocr',
                            'raw_text': raw_text,
                            'cleaned_text': cleaned_text,
                            'signal': signal,
                            'time': ocr_time
                        })
                        
                        if self._validate_signal(signal):
                            self.stats['ocr_success'] += 1
                            if self.recognition_mode == 'hybrid':
                                self.stats['color_fallback'] += 1
                            
                            total_time = time.time() - start_time
                            metadata['total_time'] = total_time
                            metadata['final_method'] = 'ocr'
                            
                            return signal, 0.8, metadata  # OCR固定置信度
                            
                except Exception as e:
                    self.logger.error(f"OCR识别出错: {e}")
                    metadata['ocr_error'] = str(e)
        
        # 所有方法都失败
        total_time = time.time() - start_time
        metadata['total_time'] = total_time
        metadata['final_method'] = 'failed'
        
        return None, 0.0, metadata
    
    def _convert_to_array(self, image: Union[str, np.ndarray, Image.Image]) -> Optional[np.ndarray]:
        """转换图像为numpy数组"""
        try:
            if isinstance(image, str):
                # 文件路径
                return cv2.imread(image)
            elif isinstance(image, np.ndarray):
                # 已经是numpy数组
                return image
            elif isinstance(image, Image.Image):
                # PIL图像
                return cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            else:
                return None
        except Exception as e:
            self.logger.error(f"图像转换失败: {e}")
            return None
    
    def _convert_to_pil(self, img_array: np.ndarray) -> Optional[Image.Image]:
        """转换numpy数组为PIL图像"""
        try:
            # BGR转RGB
            rgb_array = cv2.cvtColor(img_array, cv2.COLOR_BGR2RGB)
            return Image.fromarray(rgb_array)
        except Exception as e:
            self.logger.error(f"PIL转换失败: {e}")
            return None
    
    def _detect_by_color(self, img: np.ndarray) -> Tuple[str, float, Dict[str, int]]:
        """颜色检测方法"""
        try:
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            
            # 去除黑色背景
            bg_mask = hsv[:,:,2] < self.bg_threshold
            hsv_no_bg = hsv.copy()
            hsv_no_bg[bg_mask] = 0
            
            # 统计各种信号的像素数
            pixel_counts = {}
            
            for signal_type, ranges in self.color_ranges.items():
                total_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
                
                for range_def in ranges:
                    mask = cv2.inRange(hsv_no_bg, range_def['lower'], range_def['upper'])
                    total_mask |= mask
                
                pixel_counts[signal_type] = cv2.countNonZero(total_mask)
            
            # 找到像素数最多的信号类型
            if not pixel_counts or max(pixel_counts.values()) < self.min_pixels:
                return 'unknown', 0.0, pixel_counts
            
            # 计算置信度
            max_signal = max(pixel_counts.items(), key=lambda x: x[1])
            signal_type, max_count = max_signal
            
            total_signal_pixels = sum(pixel_counts.values())
            confidence = max_count / total_signal_pixels if total_signal_pixels > 0 else 0.0
            
            return signal_type, confidence, pixel_counts
            
        except Exception as e:
            self.logger.error(f"颜色检测出错: {e}")
            return 'unknown', 0.0, {}
    
    def _clean_ocr_text(self, text: str) -> str:
        """清洗OCR文本"""
        if not text:
            return ""
        
        # 移除特殊字符和空白
        import re
        cleaned = re.sub(r'[^\u4e00-\u9fff\w]', '', text.strip())
        return cleaned
    
    def _parse_signal_content(self, text: str) -> Optional[str]:
        """解析信号内容"""
        if not text:
            return None
        
        # 直接匹配信号关键词
        for signal in ['开仓', '清仓', '持仓', '空仓']:
            if signal in text:
                return signal
        
        return None
    
    def _validate_signal(self, signal: str) -> bool:
        """验证信号有效性"""
        return signal in ['开仓', '清仓', '持仓', '空仓']
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取识别统计信息"""
        total = self.stats['total_attempts']
        if total == 0:
            return self.stats
        
        return {
            **self.stats,
            'color_success_rate': self.stats['color_success'] / total,
            'ocr_success_rate': self.stats['ocr_success'] / total,
            'color_fallback_rate': self.stats['color_fallback'] / total,
            'overall_success_rate': (self.stats['color_success'] + self.stats['ocr_success']) / total
        }
    
    def calibrate_colors(self, sample_images: list) -> Dict[str, Any]:
        """
        颜色校准功能
        
        Args:
            sample_images: 样本图像列表，格式: [(image, expected_signal), ...]
            
        Returns:
            校准结果
        """
        calibration_data = {}
        
        for image, expected_signal in sample_images:
            img_array = self._convert_to_array(image)
            if img_array is None:
                continue
                
            # 分析颜色特征
            hsv = cv2.cvtColor(img_array, cv2.COLOR_BGR2HSV)
            bg_mask = hsv[:,:,2] > self.bg_threshold
            non_black_pixels = hsv[bg_mask]
            
            if len(non_black_pixels) > 0:
                avg_hsv = np.mean(non_black_pixels, axis=0)
                signal_key = self.signal_reverse.get(expected_signal, expected_signal)
                
                if signal_key not in calibration_data:
                    calibration_data[signal_key] = []
                calibration_data[signal_key].append(avg_hsv)
        
        # 生成新的颜色范围
        new_ranges = {}
        for signal_type, samples in calibration_data.items():
            if len(samples) < 2:
                continue
                
            samples_array = np.array(samples)
            h_min, h_max = samples_array[:, 0].min(), samples_array[:, 0].max()
            s_min, s_max = samples_array[:, 1].min(), samples_array[:, 1].max()
            v_min, v_max = samples_array[:, 2].min(), samples_array[:, 2].max()
            
            # 添加容差
            tolerance = 20
            new_ranges[signal_type] = [{
                'lower': [max(0, int(h_min - tolerance)), 
                         max(0, int(s_min - tolerance)), 
                         max(0, int(v_min - tolerance))],
                'upper': [min(180, int(h_max + tolerance)), 
                         min(255, int(s_max + tolerance)), 
                         min(255, int(v_max + tolerance))]
            }]
        
        # 保存校准结果
        with open('calibrated_color_ranges.json', 'w', encoding='utf-8') as f:
            json.dump(new_ranges, f, ensure_ascii=False, indent=2)
        
        return {
            'calibrated_signals': list(new_ranges.keys()),
            'sample_count': len(sample_images),
            'new_ranges': new_ranges
        }

def create_detector(ocr_manager=None, config=None) -> OptimizedSignalDetector:
    """创建优化的信号检测器实例"""
    return OptimizedSignalDetector(ocr_manager, config)

# 测试和演示代码
if __name__ == '__main__':
    import glob
    
    print("=== 优化信号检测器测试 ===\n")
    
    # 创建检测器
    detector = OptimizedSignalDetector()
    
    # 测试颜色识别
    test_files = glob.glob('screenshots/*.png')
    test_mapping = {
        'cc.png': '持仓', 'cc1.png': '持仓',
        'kc.png': '开仓', 'kc1.png': '开仓',
        'kk.png': '空仓', 'kk1.png': '空仓',
        'qc.png': '清仓', 'qc1.png': '清仓'
    }
    
    results = []
    
    for file_path in sorted(test_files):
        filename = os.path.basename(file_path)
        if filename not in test_mapping:
            continue
            
        expected = test_mapping[filename]
        signal, confidence, metadata = detector.detect_signal_from_image(file_path)
        
        is_correct = signal == expected
        status = "✓" if is_correct else "✗"
        
        results.append({
            'file': filename,
            'expected': expected,
            'detected': signal,
            'confidence': confidence,
            'correct': is_correct,
            'time': metadata.get('total_time', 0),
            'method': metadata.get('final_method', 'unknown')
        })
        
        print(f"{status} {filename:10s}: 预期={expected:4s} 检测={signal or 'None':4s} "
              f"置信度={confidence:.2f} 耗时={metadata.get('total_time', 0)*1000:.1f}ms "
              f"方法={metadata.get('final_method', 'unknown')}")
    
    # 统计结果
    correct_count = sum(1 for r in results if r['correct'])
    total_count = len(results)
    accuracy = correct_count / total_count if total_count > 0 else 0
    avg_time = np.mean([r['time'] for r in results]) * 1000
    
    print(f"\n=== 测试总结 ===")
    print(f"准确率: {accuracy:.1%} ({correct_count}/{total_count})")
    print(f"平均耗时: {avg_time:.1f}ms")
    
    # 显示统计信息
    stats = detector.get_statistics()
    print(f"\n=== 性能统计 ===")
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"{key}: {value:.3f}")
        else:
            print(f"{key}: {value}")
    
    # 保存测试结果
    with open('optimized_detector_test_results.json', 'w', encoding='utf-8') as f:
        json.dump({
            'test_results': results,
            'summary': {
                'accuracy': accuracy,
                'avg_time_ms': avg_time,
                'total_tests': total_count,
                'correct_tests': correct_count
            },
            'statistics': stats
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n测试结果已保存到: optimized_detector_test_results.json")
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速股票信号颜色识别器
基于颜色特征快速识别开仓/清仓/持仓/空仓信号，替代OCR方案
"""

import cv2
import numpy as np
import json
import time
import os
from typing import Tuple, Optional, Dict, Any

class FastSignalDetector:
    """快速信号检测器"""
    
    def __init__(self, config_file: str = 'signal_color_config.json'):
        """初始化检测器"""
        self.config = self._load_config(config_file)
        self.signal_names = {
            'open': '开仓',
            'close': '清仓', 
            'hold': '持仓',
            'empty': '空仓'
        }
        
        # 软件更新后的颜色范围 (2025.08.19)
        self.color_ranges = {
            'open': [  # 红色系文字 - 开仓 (HSV: 177, 147, 149)
                {'lower': np.array([170, 100, 100]), 'upper': np.array([180, 200, 200])},
                {'lower': np.array([0, 100, 100]), 'upper': np.array([10, 200, 200])}
            ],
            'close': [  # 橙色系文字 - 清仓 (HSV: 18, 252, 172)
                {'lower': np.array([8, 200, 120]), 'upper': np.array([28, 255, 220])}
            ],
            'hold': [  # 绿色系文字 - 持仓 (HSV: 67, 134, 126)
                {'lower': np.array([52, 80, 80]), 'upper': np.array([82, 180, 180])}
            ],
            'empty': [  # 灰色 - 空仓 (HSV: 0, 0, 160)
                {'lower': np.array([0, 0, 110]), 'upper': np.array([180, 30, 210])}
            ]
        }
        
        self.bg_threshold = 50  # 黑色背景阈值
        self.min_pixels = 20    # 最小像素数阈值
        
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件"""
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def detect_signal(self, image_path: str) -> Tuple[str, float, Dict[str, int]]:
        """
        检测单张图片的信号类型
        
        Args:
            image_path: 图片路径
            
        Returns:
            (signal_type, confidence, pixel_counts)
        """
        img = cv2.imread(image_path)
        if img is None:
            return 'error', 0.0, {}
            
        return self._detect_from_array(img)
    
    def detect_from_array(self, img: np.ndarray) -> Tuple[str, float, Dict[str, int]]:
        """
        从numpy数组检测信号类型
        
        Args:
            img: 图片数组
            
        Returns:
            (signal_type, confidence, pixel_counts)
        """
        return self._detect_from_array(img)
    
    def _detect_from_array(self, img: np.ndarray) -> Tuple[str, float, Dict[str, int]]:
        """内部检测方法"""
        start_time = time.time()
        
        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        
        # 去除黑色背景
        bg_mask = hsv[:,:,2] < self.bg_threshold
        hsv_no_bg = hsv.copy()
        hsv_no_bg[bg_mask] = 0
        
        # 统计各种信号的像素数
        pixel_counts = {}
        masks = {}
        
        for signal_type, ranges in self.color_ranges.items():
            total_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
            
            for range_def in ranges:
                mask = cv2.inRange(hsv_no_bg, range_def['lower'], range_def['upper'])
                total_mask |= mask
            
            masks[signal_type] = total_mask
            pixel_counts[signal_type] = cv2.countNonZero(total_mask)
        
        # 找到像素数最多的信号类型
        if not pixel_counts or max(pixel_counts.values()) < self.min_pixels:
            return 'unknown', 0.0, pixel_counts
        
        # 计算置信度
        max_signal = max(pixel_counts.items(), key=lambda x: x[1])
        signal_type, max_count = max_signal
        
        total_signal_pixels = sum(pixel_counts.values())
        confidence = max_count / total_signal_pixels if total_signal_pixels > 0 else 0.0
        
        processing_time = time.time() - start_time
        
        return signal_type, confidence, pixel_counts
    
    def batch_detect(self, image_paths: list) -> Dict[str, Dict[str, Any]]:
        """批量检测"""
        results = {}
        total_start = time.time()
        
        for img_path in image_paths:
            if not os.path.exists(img_path):
                results[img_path] = {
                    'signal': 'error',
                    'confidence': 0.0,
                    'error': 'File not found'
                }
                continue
                
            signal, confidence, pixel_counts = self.detect_signal(img_path)
            
            results[img_path] = {
                'signal': signal,
                'signal_name': self.signal_names.get(signal, signal),
                'confidence': confidence,
                'pixel_counts': pixel_counts
            }
        
        total_time = time.time() - total_start
        results['_summary'] = {
            'total_images': len(image_paths),
            'total_time': total_time,
            'avg_time_per_image': total_time / len(image_paths) if image_paths else 0
        }
        
        return results
    
    def test_accuracy(self) -> Dict[str, Any]:
        """测试准确率"""
        test_files = {
            'cc.png': 'hold', 'cc1.png': 'hold',
            'kc.png': 'open', 'kc1.png': 'open',
            'kk.png': 'empty', 'kk1.png': 'empty',
            'qc.png': 'close', 'qc1.png': 'close'
        }
        
        results = {}
        correct = 0
        total = 0
        
        for filename, expected in test_files.items():
            filepath = os.path.join('screenshots', filename)
            if not os.path.exists(filepath):
                continue
                
            signal, confidence, pixel_counts = self.detect_signal(filepath)
            is_correct = signal == expected
            
            if is_correct:
                correct += 1
            total += 1
            
            results[filename] = {
                'expected': expected,
                'detected': signal,
                'confidence': confidence,
                'correct': is_correct,
                'pixel_counts': pixel_counts
            }
        
        accuracy = correct / total if total > 0 else 0.0
        
        return {
            'results': results,
            'accuracy': accuracy,
            'correct': correct,
            'total': total
        }
    
    def optimize_thresholds(self) -> Dict[str, Any]:
        """优化检测阈值"""
        # 基于现有样本优化颜色范围
        if not self.config.get('color_analysis'):
            return {'error': 'No training data available'}
        
        optimized_ranges = {}
        
        for signal_type in ['open', 'close', 'hold', 'empty']:
            samples = []
            for filename, data in self.config['color_analysis'].items():
                if data['signal_type'] == signal_type:
                    samples.append(data['color_data']['hsv'])
            
            if not samples:
                continue
                
            # 计算HSV范围
            hsv_array = np.array(samples)
            h_min, h_max = int(hsv_array[:, 0].min()), int(hsv_array[:, 0].max())
            s_min, s_max = int(hsv_array[:, 1].min()), int(hsv_array[:, 1].max())
            v_min, v_max = int(hsv_array[:, 2].min()), int(hsv_array[:, 2].max())
            
            # 添加容差
            h_tolerance = 15
            s_tolerance = 30
            v_tolerance = 30
            
            optimized_ranges[signal_type] = [{
                'lower': [max(0, h_min - h_tolerance), 
                         max(0, s_min - s_tolerance), 
                         max(0, v_min - v_tolerance)],
                'upper': [min(180, h_max + h_tolerance), 
                         min(255, s_max + s_tolerance), 
                         min(255, v_max + v_tolerance)]
            }]
            
            # 处理红色的特殊情况（跨越0度）
            if signal_type == 'open' and h_min < 10:
                optimized_ranges[signal_type].append({
                    'lower': [max(160, 180 - h_tolerance), 
                             max(0, s_min - s_tolerance), 
                             max(0, v_min - v_tolerance)],
                    'upper': [180, 
                             min(255, s_max + s_tolerance), 
                             min(255, v_max + v_tolerance)]
                })
        
        return optimized_ranges

def main():
    """主函数 - 演示和测试"""
    print("=== 快速股票信号颜色识别器 ===\n")
    
    detector = FastSignalDetector()
    
    # 测试准确率
    print("1. 测试识别准确率...")
    accuracy_results = detector.test_accuracy()
    
    print(f"准确率: {accuracy_results['accuracy']:.1%} ({accuracy_results['correct']}/{accuracy_results['total']})")
    print("\n详细结果:")
    
    for filename, result in accuracy_results['results'].items():
        status = "✓" if result['correct'] else "✗"
        print(f"{status} {filename:10s}: 预期={detector.signal_names[result['expected']]:4s} "
              f"检测={detector.signal_names.get(result['detected'], result['detected']):4s} "
              f"置信度={result['confidence']:.2f}")
    
    # 批量测试性能
    print(f"\n2. 性能测试...")
    test_files = [os.path.join('screenshots', f) for f in 
                  ['cc.png', 'cc1.png', 'kc.png', 'kc1.png', 
                   'kk.png', 'kk1.png', 'qc.png', 'qc1.png']]
    
    batch_results = detector.batch_detect(test_files)
    summary = batch_results.pop('_summary')
    
    print(f"处理 {summary['total_images']} 张图片")
    print(f"总耗时: {summary['total_time']:.3f}s")
    print(f"平均耗时: {summary['avg_time_per_image']*1000:.1f}ms/张")
    
    # 保存结果
    with open('fast_detection_results.json', 'w', encoding='utf-8') as f:
        json.dump({
            'accuracy_test': accuracy_results,
            'batch_results': batch_results,
            'performance': summary
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n结果已保存到: fast_detection_results.json")
    
    # 生成优化建议
    print(f"\n3. 生成优化建议...")
    optimized = detector.optimize_thresholds()
    
    with open('optimized_color_ranges.json', 'w', encoding='utf-8') as f:
        json.dump(optimized, f, ensure_ascii=False, indent=2)
    
    print(f"优化配置已保存到: optimized_color_ranges.json")

if __name__ == '__main__':
    main()